"use client";

import { useState, useRef, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Icon } from '@iconify/react';
import { Sidebar as FlowbiteSidebar } from 'flowbite-react';
import SimpleBar from 'simplebar-react';
import 'simplebar-react/dist/simplebar.min.css';
import useSidebarMenu from '../hooks/useSidebarMenu';
import { MenuItem } from '../constants/menus';
import FullLogo from '../layouts/full/shared/logo/FullLogo';
import Banner from '../layouts/full/sidebar/Banner';
import { ARIA_LABELS, ARIA_ROLES, generateUniqueId } from '../utils/accessibility';

/**
 * Componente per renderizzare un elemento del menu con sottomenu
 * Implementa le best practice di accessibilità per menu espandibili
 */
const NavCollapse = ({ item }: { item: MenuItem }) => {
  const [isOpen, setIsOpen] = useState(false);
  const location = useLocation();
  const buttonId = useRef(generateUniqueId('nav-button'));
  const panelId = useRef(generateUniqueId('nav-panel'));

  const toggleCollapse = () => {
    setIsOpen(!isOpen);
  };

  // Gestisce la navigazione da tastiera
  const handleKeyDown = (e: React.KeyboardEvent) => {
    switch (e.key) {
      case 'Enter':
      case ' ':
        e.preventDefault();
        toggleCollapse();
        break;
      case 'ArrowRight':
        if (!isOpen) {
          e.preventDefault();
          setIsOpen(true);
        }
        break;
      case 'ArrowLeft':
        if (isOpen) {
          e.preventDefault();
          setIsOpen(false);
        }
        break;
    }
  };

  return (
    <div className="mb-2">
      <div
        onClick={toggleCollapse}
        onKeyDown={handleKeyDown}
        className="flex items-center justify-between p-3 text-gray-600 hover:text-primary hover:bg-gray-50 rounded-lg cursor-pointer transition-all duration-200 focus-visible"
        role={ARIA_ROLES.BUTTON}
        aria-expanded={isOpen}
        aria-controls={panelId.current}
        aria-label={`${item.label}, ${isOpen ? ARIA_LABELS.COLLAPSE : ARIA_LABELS.EXPAND}`}
        tabIndex={0}
        id={buttonId.current}
      >
        <div className="flex items-center gap-3">
          {item.icon && (
            <Icon
              icon={item.icon}
              className="h-5 w-5"
              aria-hidden="true"
            />
          )}
          <span className="font-bold">{item.label}</span>
        </div>
        <Icon
          icon={isOpen ? "solar:alt-arrow-up-line-duotone" : "solar:alt-arrow-down-line-duotone"}
          className="h-4 w-4 transition-transform duration-200"
          aria-hidden="true"
        />
      </div>

      {isOpen && item.children && (
        <div
          id={panelId.current}
          className="pl-4 mt-1 space-y-1"
          role={ARIA_ROLES.MENU}
          aria-labelledby={buttonId.current}
        >
          {item.children.map((child, index) => (
            child.children ?
              <NavCollapse key={index} item={child} /> :
              <FlowbiteSidebar.Item
                key={index}
                as={Link}
                to={child.href || '#'}
                className={`text-gray-600 hover:text-primary hover:bg-gray-50 rounded-lg transition-all duration-200 focus-visible ${
                  location.pathname === child.href ? 'bg-primary text-white' : ''
                }`}
                role={ARIA_ROLES.MENUITEM}
                aria-current={location.pathname === child.href ? 'page' : undefined}
              >
                <div className="flex items-center gap-3">
                  {child.icon && (
                    <Icon
                      icon={child.icon}
                      className="h-5 w-5"
                      aria-hidden="true"
                    />
                  )}
                  <span className="font-normal">{child.label}</span>
                </div>
              </FlowbiteSidebar.Item>
          ))}
        </div>
      )}
    </div>
  );
};

/**
 * Componente principale della sidebar
 * Implementa navigazione accessibile con supporto per screen reader e tastiera
 */
const Sidebar = () => {
  const menuItems = useSidebarMenu();
  const location = useLocation();

  return (
    <aside
      className="w-64 h-full"
      role={ARIA_ROLES.COMPLEMENTARY}
      aria-label={ARIA_LABELS.MAIN_NAVIGATION}
    >
      <FlowbiteSidebar
        id="main-navigation"
        className="fixed menu-sidebar"
        aria-label={ARIA_LABELS.MAIN_NAVIGATION}
      >
        <div className="sidebarlogo border-b border-gray-200">
          <FullLogo />
        </div>

        <SimpleBar className="h-[calc(100vh-210px)]"> {/* Altezza aggiustata per il banner fisso */}
          <FlowbiteSidebar.Items className="p-4 pb-24"> {/* Aggiunto padding bottom per evitare che il contenuto finisca sotto il banner */}
            <FlowbiteSidebar.ItemGroup className="sidebar-nav">
              {menuItems.map((section, index) => (
                <div key={index} className="caption" role={ARIA_ROLES.REGION}>
                  <h5
                    className="mb-4 text-xs font-semibold uppercase tracking-wider text-gray-500"
                    id={`section-${index}`}
                  >
                    {section.label}
                  </h5>
                  <nav
                    className="space-y-1"
                    role={ARIA_ROLES.NAVIGATION}
                    aria-labelledby={`section-${index}`}
                  >
                    {section.children?.map((item, idx) => (
                      item.children ?
                        <NavCollapse key={idx} item={item} /> :
                        <FlowbiteSidebar.Item
                          key={idx}
                          as={Link}
                          to={item.href || '#'}
                          className={`text-gray-600 hover:text-primary hover:bg-gray-50 rounded-lg transition-all duration-200 focus-visible ${
                            location.pathname === item.href ? 'bg-primary text-white' : ''
                          }`}
                          role={ARIA_ROLES.MENUITEM}
                          aria-current={location.pathname === item.href ? 'page' : undefined}
                        >
                          <div className="flex items-center gap-3">
                            {item.icon && (
                              <Icon
                                icon={item.icon}
                                className="h-5 w-5"
                                aria-hidden="true"
                              />
                            )}
                            <span className="font-bold">{item.label}</span>
                          </div>
                        </FlowbiteSidebar.Item>
                    ))}
                  </nav>
                </div>
              ))}
            </FlowbiteSidebar.ItemGroup>
          </FlowbiteSidebar.Items>
        </SimpleBar>
        <Banner />
      </FlowbiteSidebar>
    </aside>
  );
};

export default Sidebar;
