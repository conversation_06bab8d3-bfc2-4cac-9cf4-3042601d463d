/**
 * Componente di test per verificare le funzionalità di accessibilità implementate
 * Questo componente può essere utilizzato per testare e dimostrare le migliorie WCAG
 */

import React, { useState } from 'react';
import { <PERSON><PERSON>, Card } from 'flowbite-react';
import { Icon } from '@iconify/react';
import LiveRegion, { useLiveRegion } from './LiveRegion';
import {
  useFocusTrap,
  useScreenReaderAnnouncement,
  ARIA_LABELS,
  ARIA_ROLES
} from '../../utils/accessibility';

const AccessibilityTest: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [testMessage, setTestMessage] = useState('');
  const { announce } = useScreenReaderAnnouncement();
  const { announce: liveAnnounce, LiveRegionComponent } = useLiveRegion();
  const modalRef = useFocusTrap(isModalOpen);

  const handleTestAnnouncement = () => {
    const message = 'Test di annuncio per screen reader completato con successo!';
    setTestMessage(message);
    announce(message, 'polite');
    liveAnnounce(message, 'polite');
  };

  const handleUrgentAnnouncement = () => {
    const message = 'Attenzione! Questo è un annuncio urgente!';
    setTestMessage(message);
    announce(message, 'assertive');
    liveAnnounce(message, 'assertive');
  };

  const openModal = () => {
    setIsModalOpen(true);
    liveAnnounce('Modal aperto', 'polite');
  };

  const closeModal = () => {
    setIsModalOpen(false);
    liveAnnounce('Modal chiuso', 'polite');
  };

  return (
    <div className="p-6 space-y-6">
      <Card>
        <h2 className="text-2xl font-bold mb-4">Test Accessibilità WCAG 2.1 AA</h2>

        <div className="space-y-4">
          {/* Test Skip Links */}
          <section>
            <h3 className="text-lg font-semibold mb-2">Skip Links</h3>
            <p className="text-gray-600 mb-2">
              Premi Tab per vedere i skip links in azione. Sono visibili solo quando ricevono il focus.
            </p>
            <div className="bg-blue-50 p-3 rounded">
              <p className="text-sm">
                ✅ Skip links implementati nel layout principale
              </p>
            </div>
          </section>

          {/* Test Screen Reader Announcements */}
          <section>
            <h3 className="text-lg font-semibold mb-2">Annunci Screen Reader</h3>
            <div className="flex gap-2 mb-2">
              <Button
                onClick={handleTestAnnouncement}
                className="focus-visible"
                aria-describedby="announcement-help"
              >
                Test Annuncio Normale
              </Button>
              <Button
                onClick={handleUrgentAnnouncement}
                color="warning"
                className="focus-visible"
                aria-describedby="announcement-help"
              >
                Test Annuncio Urgente
              </Button>
            </div>
            <p id="announcement-help" className="text-sm text-gray-600">
              Questi pulsanti inviano annunci agli screen reader
            </p>
            {testMessage && (
              <div className="bg-green-50 p-3 rounded mt-2">
                <p className="text-sm">Ultimo messaggio: {testMessage}</p>
              </div>
            )}
          </section>

          {/* Test Focus Trap */}
          <section>
            <h3 className="text-lg font-semibold mb-2">Focus Trap</h3>
            <Button
              onClick={openModal}
              className="focus-visible"
              aria-describedby="focus-trap-help"
            >
              Apri Modal con Focus Trap
            </Button>
            <p id="focus-trap-help" className="text-sm text-gray-600 mt-2">
              Il modal mantiene il focus al suo interno e si chiude con Escape
            </p>
          </section>

          {/* Test Keyboard Navigation */}
          <section>
            <h3 className="text-lg font-semibold mb-2">Navigazione da Tastiera</h3>
            <div className="bg-blue-50 p-3 rounded">
              <p className="text-sm mb-2">✅ Navigazione implementata:</p>
              <ul className="text-sm space-y-1">
                <li>• Tab/Shift+Tab per navigare tra elementi</li>
                <li>• Enter/Space per attivare pulsanti</li>
                <li>• Frecce per navigare nei menu</li>
                <li>• Escape per chiudere modal/drawer</li>
              </ul>
            </div>
          </section>

          {/* Test ARIA Labels */}
          <section>
            <h3 className="text-lg font-semibold mb-2">Attributi ARIA</h3>
            <div className="bg-green-50 p-3 rounded">
              <p className="text-sm mb-2">✅ ARIA implementato:</p>
              <ul className="text-sm space-y-1">
                <li>• aria-label per descrizioni</li>
                <li>• aria-expanded per stati espansi</li>
                <li>• aria-current per pagina corrente</li>
                <li>• role per semantica</li>
                <li>• aria-live per annunci dinamici</li>
              </ul>
            </div>
          </section>
        </div>
      </Card>

      {/* Modal di test con focus trap */}
      {isModalOpen && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center"
          role={ARIA_ROLES.DIALOG}
          aria-modal="true"
          aria-labelledby="test-modal-title"
        >
          <div
            className="fixed inset-0 bg-gray-900/50"
            onClick={closeModal}
            aria-hidden="true"
          />
          <div
            ref={modalRef}
            className="relative bg-white p-6 rounded-lg shadow-xl max-w-md w-full mx-4"
            onKeyDown={(e) => {
              if (e.key === 'Escape') {
                closeModal();
              }
            }}
          >
            <h3 id="test-modal-title" className="text-lg font-semibold mb-4">
              Modal di Test Focus Trap
            </h3>
            <p className="mb-4">
              Questo modal dimostra il focus trap. Prova a navigare con Tab -
              il focus rimane all'interno del modal.
            </p>
            <div className="flex gap-2">
              <Button className="focus-visible">
                Pulsante 1
              </Button>
              <Button className="focus-visible">
                Pulsante 2
              </Button>
              <Button
                onClick={closeModal}
                color="gray"
                className="focus-visible"
                aria-label={ARIA_LABELS.CLOSE + " modal"}
              >
                <Icon icon="solar:close-circle-line-duotone" className="h-4 w-4" />
                Chiudi
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Live Region Component */}
      <LiveRegionComponent />
    </div>
  );
};

export default AccessibilityTest;
