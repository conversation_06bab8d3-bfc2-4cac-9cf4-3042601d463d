/**
 * Componente LiveRegion per annunci screen reader
 * Gestisce le aria-live regions per comunicare cambiamenti dinamici
 * agli utenti che utilizzano tecnologie assistive
 */

import React, { useEffect, useRef } from 'react';

interface LiveRegionProps {
  /** Messaggio da annunciare */
  message: string;
  /** Priorità dell'annuncio */
  priority?: 'polite' | 'assertive';
  /** Se true, l'intero contenuto viene riletto quando cambia */
  atomic?: boolean;
  /** Se true, il componente è visibile (per debug) */
  visible?: boolean;
  /** Callback chiamata dopo l'annuncio */
  onAnnounced?: () => void;
}

/**
 * Componente LiveRegion
 *
 * Utilizza aria-live per annunciare messaggi agli screen reader.
 * - 'polite': attende che l'utente finisca di leggere prima di annunciare
 * - 'assertive': interrompe la lettura corrente per annunciare immediatamente
 *
 * @param message - Testo da annunciare
 * @param priority - Priorità dell'annuncio ('polite' | 'assertive')
 * @param atomic - Se true, rilegge tutto il contenuto quando cambia
 * @param visible - Se true, rende il componente visibile (utile per debug)
 * @param onAnnounced - Callback eseguita dopo l'annuncio
 */
const LiveRegion: React.FC<LiveRegionProps> = ({
  message,
  priority = 'polite',
  atomic = true,
  visible = false,
  onAnnounced
}) => {
  const regionRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (message && regionRef.current) {
      // Pulisce il timeout precedente se esiste
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Aggiorna il contenuto della live region
      regionRef.current.textContent = message;

      // Chiama la callback dopo un breve delay per assicurarsi che l'annuncio sia stato fatto
      timeoutRef.current = setTimeout(() => {
        onAnnounced?.();
      }, 100);
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [message, onAnnounced]);

  return (
    <div
      ref={regionRef}
      aria-live={priority}
      aria-atomic={atomic}
      className={visible
        ? 'fixed bottom-5 right-5 bg-gray-800 text-white px-4 py-3 rounded text-sm max-w-xs z-[10000] shadow-lg'
        : 'sr-only'
      }
      role={priority === 'assertive' ? 'alert' : 'status'}
    >
      {message}
    </div>
  );
};

/**
 * Hook per gestire gli annunci live region
 * Fornisce una funzione per annunciare messaggi facilmente
 */
export const useLiveRegion = () => {
  const [announcement, setAnnouncement] = React.useState<{
    message: string;
    priority: 'polite' | 'assertive';
    id: number;
  } | null>(null);

  const announce = React.useCallback((
    message: string,
    priority: 'polite' | 'assertive' = 'polite'
  ) => {
    setAnnouncement({
      message,
      priority,
      id: Date.now() // Usa timestamp come ID unico
    });
  }, []);

  const clearAnnouncement = React.useCallback(() => {
    setAnnouncement(null);
  }, []);

  const LiveRegionComponent = React.useCallback(() => {
    if (!announcement) return null;

    return (
      <LiveRegion
        message={announcement.message}
        priority={announcement.priority}
        onAnnounced={clearAnnouncement}
      />
    );
  }, [announcement, clearAnnouncement]);

  return {
    announce,
    clearAnnouncement,
    LiveRegionComponent
  };
};

export default LiveRegion;
