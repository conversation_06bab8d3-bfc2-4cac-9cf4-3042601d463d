/**
 * Componente SkipLinks per l'accessibilità
 * Fornisce link di navigazione rapida per utenti che utilizzano screen reader
 * e navigazione da tastiera, conforme alle linee guida WCAG 2.1 AA
 */

import React from 'react';

interface SkipLink {
  href: string;
  label: string;
}

interface SkipLinksProps {
  links?: SkipLink[];
}

/**
 * Link di navigazione rapida predefiniti
 */
const defaultSkipLinks: SkipLink[] = [
  { href: '#main-content', label: 'Vai al contenuto principale' },
  { href: '#main-navigation', label: 'Vai alla navigazione principale' },
  { href: '#search', label: 'Vai alla ricerca' },
  { href: '#footer', label: 'Vai al footer' },
];

/**
 * Componente SkipLinks
 *
 * I skip links sono visibili solo quando ricevono il focus da tastiera
 * e permettono agli utenti di navigare rapidamente alle sezioni principali
 * della pagina senza dover attraversare tutti gli elementi intermedi.
 *
 * @param links - Array di link personalizzati (opzionale)
 */
const SkipLinks: React.FC<SkipLinksProps> = ({ links = defaultSkipLinks }) => {
  return (
    <nav
      aria-label="Link di navigazione rapida"
      className="fixed top-0 left-0 z-[9999]"
    >
      <ul className="list-none m-0 p-0">
        {links.map((link, index) => (
          <li key={index}>
            <a
              href={link.href}
              className="skip-link absolute -top-10 left-1.5 bg-black text-white px-4 py-2 no-underline rounded text-sm font-semibold whitespace-nowrap transition-all duration-300 border-2 border-transparent focus:top-1.5 focus:outline-2 focus:outline-white focus:outline-offset-2 hover:bg-gray-800 hover:underline sr-only focus:not-sr-only"
              onKeyDown={(e) => {
                // Gestisce la navigazione con Enter e Space
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  const target = document.querySelector(link.href);
                  if (target) {
                    // Focalizza l'elemento target
                    (target as HTMLElement).focus();
                    // Scroll smooth verso l'elemento
                    target.scrollIntoView({
                      behavior: 'smooth',
                      block: 'start'
                    });
                  }
                }
              }}
            >
              {link.label}
            </a>
          </li>
        ))}
      </ul>
    </nav>
  );
};

export default SkipLinks;
