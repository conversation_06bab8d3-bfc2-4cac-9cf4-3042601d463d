/**
 * Componente SkipLinks per l'accessibilità
 * Fornisce link di navigazione rapida per utenti che utilizzano screen reader
 * e navigazione da tastiera, conforme alle linee guida WCAG 2.1 AA
 */

import React from 'react';

interface SkipLink {
  href: string;
  label: string;
}

interface SkipLinksProps {
  links?: SkipLink[];
}

/**
 * Link di navigazione rapida predefiniti
 */
const defaultSkipLinks: SkipLink[] = [
  { href: '#main-content', label: 'Vai al contenuto principale' },
  { href: '#main-navigation', label: 'Vai alla navigazione principale' },
  { href: '#search', label: 'Vai alla ricerca' },
  { href: '#footer', label: 'Vai al footer' },
];

/**
 * Componente SkipLinks
 * 
 * I skip links sono visibili solo quando ricevono il focus da tastiera
 * e permettono agli utenti di navigare rapidamente alle sezioni principali
 * della pagina senza dover attraversare tutti gli elementi intermedi.
 * 
 * @param links - Array di link personalizzati (opzionale)
 */
const SkipLinks: React.FC<SkipLinksProps> = ({ links = defaultSkipLinks }) => {
  return (
    <nav 
      aria-label="Link di navigazione rapida"
      className="skip-links"
    >
      <ul className="skip-links-list">
        {links.map((link, index) => (
          <li key={index}>
            <a
              href={link.href}
              className="skip-link"
              onKeyDown={(e) => {
                // Gestisce la navigazione con Enter e Space
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  const target = document.querySelector(link.href);
                  if (target) {
                    // Focalizza l'elemento target
                    (target as HTMLElement).focus();
                    // Scroll smooth verso l'elemento
                    target.scrollIntoView({ 
                      behavior: 'smooth', 
                      block: 'start' 
                    });
                  }
                }
              }}
            >
              {link.label}
            </a>
          </li>
        ))}
      </ul>
      
      <style jsx>{`
        .skip-links {
          position: absolute;
          top: 0;
          left: 0;
          z-index: 9999;
        }
        
        .skip-links-list {
          list-style: none;
          margin: 0;
          padding: 0;
        }
        
        .skip-link {
          position: absolute;
          top: -40px;
          left: 6px;
          background: #000;
          color: #fff;
          padding: 8px 16px;
          text-decoration: none;
          border-radius: 4px;
          font-size: 14px;
          font-weight: 600;
          white-space: nowrap;
          transition: top 0.3s ease;
          border: 2px solid transparent;
        }
        
        .skip-link:focus {
          top: 6px;
          outline: 2px solid #fff;
          outline-offset: 2px;
        }
        
        .skip-link:hover {
          background: #333;
          text-decoration: underline;
        }
        
        /* Assicura che i link siano sempre accessibili */
        .skip-link:not(:focus):not(:active) {
          clip: rect(0 0 0 0);
          clip-path: inset(50%);
          height: 1px;
          overflow: hidden;
          position: absolute;
          white-space: nowrap;
          width: 1px;
        }
      `}</style>
    </nav>
  );
};

export default SkipLinks;
