@import url('https://fonts.googleapis.com/css2?family=Manrope:wght@200..800&display=swap');
@import './layouts/container.css';
@import './layouts/sidebar.css';
@import './layouts/header.css';
@import './theme/default-colors.css';
@import './override/reboot.css';
@import './responsive/buttons.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  /* Wrapper per gestire gruppi di bottoni su mobile */
  .button-wrapper {
    @apply flex flex-wrap gap-2 justify-end;
  }

  /* Gestione responsive dei bottoni */
  @media (max-width: 640px) {
    .button-wrapper {
      @apply w-full justify-center;
    }

    .button-wrapper button,
    .button-wrapper a {
      @apply w-full;
    }
  }

  /* Container principale */
  .container {
    @apply max-w-[1200px] px-30;
  }

  .landingpage .container {
    @apply max-w-[1320px];
  }

  html {
    @apply overflow-x-hidden;
  }

  body {
    @apply text-sm text-bodytext;
  }

  .dropdown {
    @apply shadow-md bg-white dark:bg-dark relative rounded-md dark:shadow-dark-md overflow-hidden;
  }

  .card-title {
    @apply text-lg font-semibold text-dark dark:text-white mb-1;
  }

  .card-subtitle {
    @apply text-15 text-bodytext;
  }

  /*heading text color*/
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply text-dark dark:text-white font-semibold;
  }

  .border-ld {
    @apply border-border dark:border-darkborder;
  }

  .bg-hover {
    @apply hover:bg-lighthover hover:dark:bg-darkmuted;
  }

  .form-control input {
    @apply rounded-md border border-border dark:border-darkborder bg-transparent dark:bg-transparent w-full text-sm;
  }

  .form-control-chat input {
    @apply rounded-md border-0 bg-transparent dark:bg-transparent w-full text-sm;
  }

  .form-control-chat input:focus {
    @apply !border-0 bg-transparent dark:bg-transparent w-full text-sm ring-transparent dark:ring-transparent;
  }

  .form-control-rounded input {
    @apply rounded-full border border-border dark:border-darkborder bg-transparent dark:bg-transparent w-full text-sm;
  }

  .form-control-rounded input:focus {
    @apply border-primary dark:border-primary outline-none shadow-none ring-offset-0 ring-transparent;
  }

  .form-control-textarea {
    @apply !rounded-md border border-border dark:border-darkborder bg-transparent dark:bg-transparent w-full text-sm p-4;
  }

  .form-control-textarea:focus {
    @apply border-primary dark:border-primary outline-none shadow-none ring-offset-0 ring-transparent;
  }

  .form-control input:focus {
    @apply border-primary dark:border-primary outline-none shadow-none ring-offset-0 ring-transparent;
  }

  .form-control-input {
    @apply rounded-md border border-border dark:border-darkborder bg-transparent dark:bg-transparent w-full text-sm;
  }

  .form-control-input:focus {
    @apply border-primary dark:border-primary outline-none shadow-none ring-offset-0 ring-transparent;
  }

  .form-control-validation input {
    @apply rounded-md;
  }

  .form-rounded-md input {
    @apply rounded-md;
  }

  .input-center input {
    @apply text-center;
  }

  .elipse {
    @apply w-[18px] h-[10px];
  }

  input::placeholder {
    @apply text-bodytext;
  }

  .select-option select {
    @apply bg-muted border-0 text-darklink dark:text-white/80 py-2 ps-4 pe-9 w-auto focus:border-0 focus:ring-0 font-medium;
  }

  .select-md select {
    @apply border-ld bg-transparent dark:bg-darkgray w-full text-sm rounded-md focus:border-primary dark:focus:border-primary focus:ring-0;
  }

  .checkbox {
    @apply h-[18px] w-[18px] border border-border dark:border-darkborder bg-transparent focus:ring-0 focus:ring-offset-0;
  }

  .text-primary-ld {
    @apply hover:text-primary dark:hover:text-primary;
  }

  /* Apps */
  .left-part {
    @apply w-80 border-e border-ld p-6;
  }

  .btn-circle {
    @apply h-8 w-8 !rounded-full flex justify-center items-center p-0;
  }

  .btn-circle-hover {
    @apply h-9 w-9 flex justify-center items-center !rounded-full hover:bg-lightprimary hover:text-primary cursor-pointer bg-transparent;
  }

  .text-ld {
    @apply text-dark dark:text-white;
  }

  .avatar-full img {
    @apply w-full h-full;
  }

  .sorting button {
    @apply bg-transparent text-dark dark:text-white p-0;
  }

  .sorting button:hover {
    @apply bg-transparent;
  }

  .sorting button span {
    @apply p-0;
  }

  .sorting ul li {
    @apply px-4 py-2;
  }

  .sorting ul li button:hover {
    @apply hover:text-primary;
  }

  /* ===== ACCESSIBILITÀ WCAG 2.1 AA ===== */

  /* Screen reader only - nasconde visivamente ma mantiene accessibile */
  .sr-only {
    @apply absolute w-px h-px p-0 -m-px overflow-hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  /* Focus visibile per navigazione da tastiera */
  .focus-visible {
    @apply outline-2 outline-offset-2 outline-primary;
  }

  /* Skip links per navigazione rapida */
  .skip-link {
    @apply absolute -top-10 left-2 bg-gray-900 text-white px-4 py-2 rounded;
    @apply transition-all duration-300 z-50 font-medium;
  }

  .skip-link:focus {
    @apply top-2 outline-2 outline-offset-2 outline-white;
  }

  /* Miglioramenti focus per elementi interattivi */
  button:focus-visible,
  a:focus-visible,
  input:focus-visible,
  select:focus-visible,
  textarea:focus-visible {
    @apply outline-2 outline-offset-2 outline-primary;
  }

  /* Indicatori di stato per screen reader */
  .loading-indicator::after {
    content: 'Caricamento in corso...';
    @apply sr-only;
  }

  .error-indicator::after {
    content: 'Errore';
    @apply sr-only;
  }

  .success-indicator::after {
    content: 'Operazione completata';
    @apply sr-only;
  }

  /* Miglioramenti contrasto per testo */
  .high-contrast {
    @apply text-gray-900 dark:text-white;
  }

  .high-contrast-bg {
    @apply bg-white dark:bg-gray-900;
  }

  /* Dimensioni touch target minime (44px x 44px) */
  .touch-target {
    @apply min-h-[44px] min-w-[44px] flex items-center justify-center;
  }

  /* Animazioni rispettose delle preferenze utente */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}
