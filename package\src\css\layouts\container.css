/* Stile per il contenitore principale */
.main-content {
  width: 100%;
  padding: 1rem;
  transition: all 0.3s ease;
}

/* Desktop */
@media screen and (min-width: 1280px) {
  .main-content {
    padding: 1.5rem;
  }
}

/* Mobile */
@media screen and (max-width: 640px) {
  .main-content {
    padding: 0.75rem;
  }

  /* Aggiungi padding orizzontale ridotto per i contenitori interni */
  .main-content > div {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
}
