.top-header {
  margin-left: 300px;
  margin-right: 180px;
  position: sticky;
  top: 0;
  z-index: 99;
  transition: all 0.2s ease-in;
}

@media (max-width: 1280px) {
  .top-header {
    margin-left: 0;
    margin-right: 0;
    width: 100% !important;
  }
}

.mobile-header-menu {
  height: 0;
  overflow: hidden;
  transition: all 0.3s ease-in-out;
}

.mobile-header-menu.active {
  min-height: 70px;
  overflow: visible;
}

@media (min-width: 1300px) {
  html[data-layout='vertical'] .top-header {
    width: calc(100% - 400px);
  }
}
