.minisidebar-icon {
  position: fixed;
  left: 0;
  height: 100%;
  top: 0;
  background-color: #f4f7fb;
  width: 80px;
}

/* Left sidebar */
.menu-sidebar {
  position: fixed;
  height: 100vh;
  width: 300px !important;
  background: var(--bs-white);
  top: 62px;
  left: 0;
  z-index: 100;
  transition: all 0.3s ease;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  border-right: 1px solid rgb(229, 231, 235);
}

/* Banner container styles */
.menu-sidebar > div:last-child {
  background: white;
  border-top: 1px solid rgb(229, 231, 235);
  z-index: 101;
}

/* Contenuto principale della sidebar */
.sidebar-nav {
  padding: 0.5rem;
}

/* Right sidebar */
.right-sidebar {
  position: fixed;
  height: calc(100vh - 62px);
  width: 180px !important;
  background: var(--bs-white);
  top: 62px;
  right: 0;
  z-index: 10;
  transition: all 0.3s ease;
  box-shadow: -1px 0 4px rgba(0, 0, 0, 0.05);
  border-left: 1px solid rgb(229, 231, 235);
}

.sidebar-nav .caption {
  padding: 1.25rem 0 0.5rem !important;
  margin-bottom: 0.5rem !important;
  border-top: 1px solid var(--color-border);
}

.sidebar-nav .caption:first-child {
  padding-top: 0 !important;
  border-top: none;
}

.sidebar-nav .caption h5 {
  color: rgb(107, 114, 128);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Stile per gli elementi del menu */
.sidebar-nav .flowbite-sidebar-item {
  padding: 0.75rem 1rem !important;
  margin: 0.25rem 0 !important;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
  color: var(--color-link);
}

.sidebar-nav .flowbite-sidebar-item:hover {
  background-color: rgb(243, 244, 246);
  color: var(--color-primary);
}

.sidebar-nav .flowbite-sidebar-item.active {
  background-color: var(--color-primary);
  color: white;
}

/* Spacing per gli item con icone */
.sidebar-nav .flowbite-sidebar-item .flex {
  gap: 12px;
  align-items: center;
}

/* Styling per il testo del menu */
.sidebar-nav .flowbite-sidebar-item span {
  font-size: 14px;
  line-height: 1.5;
}

/* Miglior allineamento per i sottomenu */
.sidebar-dropdown {
  padding-left: 12px;
  margin-top: 4px;
}

.sidebar-dropdown li a {
  padding: 8px 16px;
  margin-bottom: 2px;
  display: flex;
  align-items: center;
  gap: 12px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.sidebar-dropdown li a.active {
  opacity: 1;
  color: var(--color-primary) !important;
}

.sidebar-dropdown li a {
  background-color: transparent !important;
}

.sidebar-dropdown li a:hover {
  background-color: transparent !important;
  opacity: 1;
}

.sidebar-dropdown li button:hover {
  background-color: transparent !important;
}

.sidebar-dropdown li .iconify--fad {
  height: 9px;
  width: 9px;
  margin: 0 4px;
}

.barnd-logo {
  min-height: 72px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.nav-link {
  display: block;
  font-size: 14px;
  color: #98a4ae;
  text-decoration: none;
  background: 0 0;
  border: 0;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out;
}

@media screen and (max-width: 1280px) {
  .menu-sidebar {
    transform: translateX(-100%);
    top: 0;
    width: 280px !important;
  }

  .right-sidebar {
    transform: translateX(100%);
    width: 250px !important;
  }

  .menu-sidebar.active {
    transform: translateX(0);
    box-shadow: 4px 0 15px rgba(0, 0, 0, 0.1);
  }

  .right-sidebar.active {
    transform: translateX(0);
  }
}

/* Small mobile devices */
@media screen and (max-width: 640px) {
  .menu-sidebar,
  .right-sidebar {
    width: 100% !important;
  }

  /* Miglioramenti per la navigazione mobile */
  .mobile-nav-item {
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
  }

  .mobile-nav-item:hover {
    background-color: rgba(var(--color-primary-rgb), 0.1);
  }

  .mobile-nav-item.active {
    background-color: var(--color-primary);
    color: white;
  }

  /* Stile per i drawer mobili */
  .mobile-drawer {
    max-width: 100%;
    width: 100%;
    height: 100vh;
    overflow: hidden;
  }

  @media (min-width: 640px) {
    .mobile-drawer {
      max-width: 320px;
    }
  }

  /* Animazioni per i drawer mobili */
  @keyframes slideInLeft {
    from {
      transform: translateX(-100%);
    }
    to {
      transform: translateX(0);
    }
  }

  @keyframes slideInRight {
    from {
      transform: translateX(100%);
    }
    to {
      transform: translateX(0);
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  .drawer-left-enter {
    animation: slideInLeft 0.25s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    box-shadow: 4px 0 25px rgba(0, 0, 0, 0.15);
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
  }

  .drawer-right-enter {
    animation: slideInRight 0.25s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    box-shadow: -4px 0 25px rgba(0, 0, 0, 0.15);
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
  }

  .drawer-overlay-enter {
    animation: fadeIn 0.2s ease-out forwards;
    backdrop-filter: blur(2px);
  }
}

/* Gestione scroll della sidebar */
.simplebar-content-wrapper {
  padding: 0 !important;
}

/* Gestione scroll della sidebar */
.simplebar-track {
  background-color: transparent;
}

.simplebar-scrollbar::before {
  background: rgb(209, 213, 219);
}

/* Logo container */
.sidebarlogo {
  height: 62px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0 1.25rem;
  background: white;
  border-bottom: 1px solid rgb(229, 231, 235);
}
