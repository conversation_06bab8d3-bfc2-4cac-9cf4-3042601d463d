import { useState, useEffect } from "react";
import { Navbar, Button } from "flowbite-react";
import { Icon } from "@iconify/react";
import MobileSidebar from "../sidebar/MobileSidebar";
import MobileRightSidebar from "../sidebar/MobileRightSidebar";
import { useFocusTrap, ARIA_LABELS, ARIA_ROLES } from "../../../utils/accessibility";

const Header = () => {
  const [isSticky, setIsSticky] = useState(false);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isRightSidebarOpen, setIsRightSidebarOpen] = useState(false);

  // Focus trap per i drawer mobili
  const leftDrawerRef = useFocusTrap(isSidebarOpen);
  const rightDrawerRef = useFocusTrap(isRightSidebarOpen);

  useEffect(() => {
    const handleScroll = () => {
      setIsSticky(window.scrollY > 50);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Chiudi i drawer quando la finestra viene ridimensionata a desktop
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) { // lg breakpoint
        setIsSidebarOpen(false);
        setIsRightSidebarOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Gestione della chiusura del menu di sinistra
  const closeSidebar = () => {
    setIsSidebarOpen(false);
  };

  // Gestione della chiusura del menu di destra
  const closeRightSidebar = () => {
    setIsRightSidebarOpen(false);
  };

  // Previene la propagazione del click all'interno del menu
  const handleMenuClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  // Gestisce la chiusura con Escape e previene lo scroll del body
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        if (isSidebarOpen) {
          closeSidebar();
        }
        if (isRightSidebarOpen) {
          closeRightSidebar();
        }
      }
    };

    if (isSidebarOpen || isRightSidebarOpen) {
      document.addEventListener('keydown', handleEscape);
      // Previene lo scroll del body quando i drawer sono aperti
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isSidebarOpen, isRightSidebarOpen]);

  return (
    <>
      <header
        className={`sticky top-0 z-[5] ${
          isSticky ? "bg-white dark:bg-dark shadow-md" : "bg-white dark:bg-darkgray"
        }`}
      >
        <Navbar
          fluid
          className="bg-transparent dark:bg-transparent py-2 px-4 md:px-6"
        >
          <div className="flex items-center justify-between w-full">
            {/* Left side */}
            <div className="flex items-center">
              <Button
                color="light"
                size="sm"
                className="lg:hidden mr-2 p-2 touch-target focus-visible"
                onClick={() => setIsSidebarOpen(true)}
                aria-label={ARIA_LABELS.OPEN + " menu di navigazione"}
                aria-expanded={isSidebarOpen}
                aria-controls="mobile-sidebar"
              >
                <Icon
                  icon="solar:hamburger-menu-line-duotone"
                  className="h-5 w-5"
                  aria-hidden="true"
                />
                <span className="sr-only">Apri menu di navigazione</span>
              </Button>
              <span className="text-sm font-medium lg:hidden">MOKO SOSTANZA Dental CRM</span>
            </div>

            {/* Right side */}
            <div className="flex items-center gap-2">
              <Button
                color="light"
                size="sm"
                className="lg:hidden p-2 touch-target focus-visible"
                onClick={() => setIsRightSidebarOpen(true)}
                aria-label={ARIA_LABELS.OPEN + " pannello laterale"}
                aria-expanded={isRightSidebarOpen}
                aria-controls="mobile-right-sidebar"
              >
                <Icon
                  icon="solar:widget-add-line-duotone"
                  className="h-5 w-5"
                  aria-hidden="true"
                />
                <span className="sr-only">Apri pannello laterale</span>
              </Button>
            </div>
          </div>
        </Navbar>
      </header>

      {/* Mobile Left Sidebar - Implementazione manuale del drawer */}
      {isSidebarOpen && (
        <div className="lg:hidden" role={ARIA_ROLES.DIALOG} aria-modal="true">
          {/* Overlay per chiudere il drawer quando si clicca fuori */}
          <div
            className="fixed inset-0 bg-gray-900/50 dark:bg-gray-900/80 z-40 drawer-overlay-enter"
            onClick={closeSidebar}
            aria-hidden="true"
          ></div>

          {/* Drawer content */}
          <div
            ref={leftDrawerRef}
            id="mobile-sidebar"
            className="fixed top-0 left-0 z-50 h-screen w-[280px] max-w-[85%] bg-white dark:bg-gray-800 overflow-y-auto drawer-left-enter shadow-xl"
            onClick={handleMenuClick}
            role={ARIA_ROLES.NAVIGATION}
            aria-label={ARIA_LABELS.MAIN_NAVIGATION}
            onKeyDown={(e) => {
              if (e.key === 'Escape') {
                closeSidebar();
              }
            }}
          >
            <MobileSidebar onClose={closeSidebar} />
          </div>
        </div>
      )}

      {/* Mobile Right Sidebar - Implementazione manuale del drawer */}
      {isRightSidebarOpen && (
        <div className="lg:hidden" role={ARIA_ROLES.DIALOG} aria-modal="true">
          {/* Overlay per chiudere il drawer quando si clicca fuori */}
          <div
            className="fixed inset-0 bg-gray-900/50 dark:bg-gray-900/80 z-40 drawer-overlay-enter"
            onClick={closeRightSidebar}
            aria-hidden="true"
          ></div>

          {/* Drawer content */}
          <div
            ref={rightDrawerRef}
            id="mobile-right-sidebar"
            className="fixed top-0 right-0 z-50 h-screen w-[280px] max-w-[85%] bg-white dark:bg-gray-800 overflow-y-auto drawer-right-enter shadow-xl"
            onClick={handleMenuClick}
            role={ARIA_ROLES.COMPLEMENTARY}
            aria-label="Pannello laterale"
            onKeyDown={(e) => {
              if (e.key === 'Escape') {
                closeRightSidebar();
              }
            }}
          >
            <MobileRightSidebar onClose={closeRightSidebar} />
          </div>
        </div>
      )}
    </>
  );
};

export default Header;
