/**
 * Utility per l'accessibilità e conformità WCAG 2.1 AA
 * Fornisce funzioni helper per migliorare l'accessibilità dell'applicazione
 */

import { useEffect, useRef } from 'react';

/**
 * Hook per gestire il focus trap nei modal e drawer
 * Mantiene il focus all'interno del contenitore specificato
 */
export const useFocusTrap = (isActive: boolean) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const previousActiveElement = useRef<HTMLElement | null>(null);

  useEffect(() => {
    if (!isActive || !containerRef.current) return;

    // Salva l'elemento attualmente focalizzato
    previousActiveElement.current = document.activeElement as HTMLElement;

    const container = containerRef.current;
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );

    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    // Focalizza il primo elemento
    if (firstElement) {
      firstElement.focus();
    }

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;

      if (e.shiftKey) {
        // Shift + Tab
        if (document.activeElement === firstElement) {
          e.preventDefault();
          lastElement?.focus();
        }
      } else {
        // Tab
        if (document.activeElement === lastElement) {
          e.preventDefault();
          firstElement?.focus();
        }
      }
    };

    const handleEscapeKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        // Trigger close event
        const closeEvent = new CustomEvent('focustrap:escape');
        container.dispatchEvent(closeEvent);
      }
    };

    container.addEventListener('keydown', handleTabKey);
    container.addEventListener('keydown', handleEscapeKey);

    return () => {
      container.removeEventListener('keydown', handleTabKey);
      container.removeEventListener('keydown', handleEscapeKey);

      // Ripristina il focus all'elemento precedente
      if (previousActiveElement.current) {
        previousActiveElement.current.focus();
      }
    };
  }, [isActive]);

  return containerRef;
};

/**
 * Hook per gestire gli annunci per screen reader
 * Utilizza aria-live regions per comunicare cambiamenti dinamici
 */
export const useScreenReaderAnnouncement = () => {
  const announce = (message: string, priority: 'polite' | 'assertive' = 'polite') => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', priority);
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;

    document.body.appendChild(announcement);

    // Rimuovi l'elemento dopo un breve delay
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  };

  return { announce };
};

/**
 * Hook per gestire la navigazione da tastiera
 * Fornisce handlers comuni per la navigazione con frecce e Enter/Space
 */
export const useKeyboardNavigation = (
  items: HTMLElement[],
  onSelect?: (index: number) => void
) => {
  const currentIndex = useRef(0);

  const handleKeyDown = (e: KeyboardEvent) => {
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        currentIndex.current = Math.min(currentIndex.current + 1, items.length - 1);
        items[currentIndex.current]?.focus();
        break;

      case 'ArrowUp':
        e.preventDefault();
        currentIndex.current = Math.max(currentIndex.current - 1, 0);
        items[currentIndex.current]?.focus();
        break;

      case 'Home':
        e.preventDefault();
        currentIndex.current = 0;
        items[0]?.focus();
        break;

      case 'End':
        e.preventDefault();
        currentIndex.current = items.length - 1;
        items[items.length - 1]?.focus();
        break;

      case 'Enter':
      case ' ':
        e.preventDefault();
        onSelect?.(currentIndex.current);
        break;
    }
  };

  return { handleKeyDown, currentIndex: currentIndex.current };
};

/**
 * Genera un ID unico per associare label e controlli
 */
export const generateId = (prefix: string = 'id'): string => {
  return `${prefix}-${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * Verifica se un elemento è visibile per gli screen reader
 */
export const isElementAccessible = (element: HTMLElement): boolean => {
  const style = window.getComputedStyle(element);
  return !(
    style.display === 'none' ||
    style.visibility === 'hidden' ||
    element.hasAttribute('aria-hidden') ||
    element.getAttribute('aria-hidden') === 'true'
  );
};

/**
 * Calcola il contrasto tra due colori per verificare conformità WCAG
 */
export const calculateContrast = (color1: string, color2: string): number => {
  // Implementazione semplificata del calcolo del contrasto
  // In un'implementazione reale, si dovrebbe usare una libreria dedicata
  const getLuminance = (color: string): number => {
    // Conversione semplificata - in produzione usare una libreria come chroma-js
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16) / 255;
    const g = parseInt(hex.substr(2, 2), 16) / 255;
    const b = parseInt(hex.substr(4, 2), 16) / 255;

    const sRGB = [r, g, b].map(c => {
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });

    return 0.2126 * sRGB[0] + 0.7152 * sRGB[1] + 0.0722 * sRGB[2];
  };

  const lum1 = getLuminance(color1);
  const lum2 = getLuminance(color2);
  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);

  return (brightest + 0.05) / (darkest + 0.05);
};

/**
 * Verifica se il contrasto soddisfa i requisiti WCAG AA
 */
export const meetsWCAGContrast = (
  color1: string,
  color2: string,
  level: 'AA' | 'AAA' = 'AA',
  isLargeText: boolean = false
): boolean => {
  const contrast = calculateContrast(color1, color2);
  const threshold = level === 'AAA'
    ? (isLargeText ? 4.5 : 7)
    : (isLargeText ? 3 : 4.5);

  return contrast >= threshold;
};

/**
 * Genera un ID unico per associare label e controlli
 */
export const generateUniqueId = (prefix: string = 'field'): string => {
  return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * Costanti per attributi ARIA comuni
 */
export const ARIA_LABELS = {
  // Navigazione
  MAIN_NAVIGATION: 'Navigazione principale',
  BREADCRUMB: 'Percorso di navigazione',
  PAGINATION: 'Navigazione pagine',

  // Azioni
  CLOSE: 'Chiudi',
  OPEN: 'Apri',
  EXPAND: 'Espandi',
  COLLAPSE: 'Comprimi',
  SEARCH: 'Cerca',
  FILTER: 'Filtra',
  SORT: 'Ordina',

  // Stato
  LOADING: 'Caricamento in corso',
  ERROR: 'Errore',
  SUCCESS: 'Operazione completata con successo',
  WARNING: 'Attenzione',

  // Form
  REQUIRED_FIELD: 'Campo obbligatorio',
  OPTIONAL_FIELD: 'Campo opzionale',
  INVALID_INPUT: 'Input non valido',

  // Modal e Dialog
  MODAL: 'Finestra di dialogo',
  ALERT_DIALOG: 'Finestra di avviso',
  CONFIRMATION_DIALOG: 'Finestra di conferma',
} as const;

/**
 * Ruoli ARIA comuni
 */
export const ARIA_ROLES = {
  BUTTON: 'button',
  LINK: 'link',
  MENU: 'menu',
  MENUITEM: 'menuitem',
  MENUBAR: 'menubar',
  TAB: 'tab',
  TABPANEL: 'tabpanel',
  TABLIST: 'tablist',
  DIALOG: 'dialog',
  ALERTDIALOG: 'alertdialog',
  ALERT: 'alert',
  STATUS: 'status',
  REGION: 'region',
  BANNER: 'banner',
  MAIN: 'main',
  NAVIGATION: 'navigation',
  COMPLEMENTARY: 'complementary',
  CONTENTINFO: 'contentinfo',
} as const;
