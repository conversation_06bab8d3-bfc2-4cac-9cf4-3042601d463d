// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import React from 'react';
import CardBox from '../../components/shared/CardBox';


const SamplePage = () => {
  return (
    <CardBox>
    <h5 className="card-title">Sample page</h5>
    <p>
      Lorem Ipsum is simply dummy text of the printing and typesetting
      industry. Lorem Ipsum has been the industry's standard dummy text ever
      since the 1500s
    </p>
  </CardBox>
  );
};

export default SamplePage;
