import { Badge, Dropdown } from "flowbite-react";
import { Icon } from "@iconify/react";
import { Link } from "react-router";


const Notifications = [
    {
        id:1,
        title:"Item1",
    },
    {
        id:2,
        title:"Item2",
    },

]

const Notification = () => {
    return (
        <div className="relative group/menu">
            <Dropdown label="" className="rounded-sm w-[150px] notification" dismissOnClick={false} renderTrigger={() => (
                <span
                    className="h-10 w-10 text-white hover:text-primary group-hover/menu:bg-white group-hover/menu:text-primary hover:bg-white rounded-full flex justify-center items-center cursor-pointer relative"
                    aria-label="Notifications"
                >
                    <Icon icon="solar:bell-linear" height={20} />
                    <Badge className="h-2 w-2 rounded-full absolute end-2 top-1 bg-white p-0" />
                </span>
            )}
            >
                {
                    Notifications.map((item) => (
                        <Dropdown.Item as={Link} key={item.id} to="#" className="px-3 py-2 flex items-center bg-hover group/link w-full gap-3 text-dark hover:bg-gray-100">
                          {item?.title}
                    </Dropdown.Item>
                    ))
                }
            </Dropdown>
        </div>
    );
};

export default Notification;
